package com.think1024.tocodesign.ideaplugin.webview

import com.google.gson.JsonObject
import com.intellij.openapi.diagnostic.Logger
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.services.UserService
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettingsComponent.ColorButton
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.IdeaConfig
import java.util.*
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.DelicateCoroutinesApi

// WebViewManager.kt
object WebViewManager {
    private val logger = Logger.getInstance(WebViewManager::class.java)
    private val webViews = mutableMapOf<String, MutableList<TocoBrowser>>()

    fun register(project: Project? = null, browser: TocoBrowser) {
        var webViewList = getAllBrowsers(project)
        webViewList?.add(browser)
    }

    fun unregister(project: Project? = null, browser: TocoBrowser) {
        var webViewList = getAllBrowsers(project)
        webViewList?.remove(browser)
    }

    fun cleanWebViews(project: Project? = null) {
        var key = project?.hashCode()?.toString() ?: ""
        if (key.isNotEmpty()) {
            var webViewList = webViews[key]?.toList()
            webViewList?.forEach { it.dispose() }
            webViews.remove(key)
        }
    }

    /**
     * 获取所有注册的浏览器实例
     *
     * @return 所有注册的 TocoBrowser 实例的列表
     */
    fun getAllBrowsers(project: Project? = null): MutableList<TocoBrowser>? {
        var key = project?.hashCode()?.toString() ?: ""
        if (key.isEmpty()) {
            logger.warn("[WebViewManager getAllBrowsers]: Project is null, unable to retrieve web views.")
            return null
        }
        var webViewList = webViews[key]
        if (webViewList == null) {
            webViewList = mutableListOf()
            webViews[key] = webViewList
        }
        return webViewList
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun setBrowserTheme(browser: TocoBrowser, theme: String) {
        GlobalScope.launch {
            // 如果用sendToWebview会阻塞，因为当时系统的设置弹窗存在，会等设置弹窗消失后js才会执行，原因未知
            browser.sendToWebviewDirectly("setAction", JsonObject().apply {
                addProperty("type", "persist/setTheme")
                addProperty("payload", theme)
            })
        }
    }

    fun syncStoreAction(project: Project?, store: Any, browser: TocoBrowser) {
        if (project == null) {
            return
        }
        var webViewList = getAllBrowsers(project)
        // TODO: 如果是persist，需要更新idea的persist存储
        val key = getKey(browser)
        val frames = getFrames(project).map { it.getBrowser() }
        if (frames.find { it === browser } != null || key.isEmpty()) {
            // action同步给所有页面
            // 1. 框架页面
            // 2. 没有key的页面，例如设置页
            webViewList?.forEach {
                if (it != browser) {
                    sendAction(it, store)
                }
            }
        } else {
            // 有key的页面，同步给key相同的页面、弹窗页、框架
            val pages = getRelatedPages(project, browser)
            pages?.forEach { sendAction(it, store) }
            frames.forEach { sendAction(it, store) }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun sendAction(browser: TocoBrowser, store: Any) {
        GlobalScope.launch {
            browser.sendToWebview("setAction", store)
        }
    }

    /**
     * 找出所有uuid组成一个页面key
     */
    private fun getKey(browser: TocoBrowser): String {
        val url = browser.getURL().trim()
        return url.split("/").filter {
            try {
                UUID.fromString(it)
                true
            } catch (_: Exception) {
                false
            }
        }.joinToString("_")
    }

    /**
     * 获取所有非框架页面，即所有tab页
     */
    private fun getRelatedPages(project: Project, browser: TocoBrowser): List<TocoBrowser>? {
        var webViewList = getAllBrowsers(project)
        return webViewList?.filter { browser != it && getKey(browser) == getKey(it) && !browser.config.isFrame }
    }

    /**
     * 获取所有框架页面，左侧树、右侧chat、底部编译结果
     */
    private fun getFrames(project: Project): List<WebViewLoader> {
        return WindowIds.ALL.toList().map {
            TocoBrowserManager.getInstance(project).getBrowser(it)
        }
    }

    /**
     * 从一个页面中获取其整个store，1秒超时
     */
    private suspend fun getStoreValue(browser: TocoBrowser): JsonObject? {
        return try {
            // 使用 withTimeout 添加 1 秒超时
            kotlinx.coroutines.withTimeout(1000) {
                browser.sendToWebview("getStore", {}, true)
            }
        } catch (e: kotlinx.coroutines.TimeoutCancellationException) {
            println("getStoreValue timeout after 1 second")
            null
        } catch (e: Exception) {
            println("getStoreValue error: ${e.message}")
            null
        }
    }

    /**
     * 构造persist的store数据
     */
    private fun buildPersist(project: Project, userInfo: ApplicationPluginSettings): JsonObject {
        return JsonObject().apply {
            add("project", JsonObject().apply {
                addProperty("name", project.name)
                addProperty("uuid", ProjectPluginSettings.getInstance(project).projectId)
            })
            add("user", JsonObject().apply {
                addProperty("id", userInfo.userId)
                addProperty("nickname", userInfo.nickname)
                addProperty("preferredUsername", userInfo.preferredUsername)
                addProperty("avatar", userInfo.avatar)
                addProperty("username", userInfo.username)
                addProperty("isPm", userInfo.isPm)
            })
            addProperty("locale", "zh-CN")
            addProperty("theme", IdeaConfig.getCurrentTheme())
            addProperty("primaryColor", "#DFC193")
            addProperty("siderCollapsed", false)
            addProperty("footerCollapsed", false)
            addProperty("online", false)
            addProperty("host", "")
            addProperty("maxProcessCount", 10)
            addProperty("preMergeMode", "commit")
            addProperty("confirmPreMergeMode", true)
        }
    }

    /**
     * 把从各个页面获取到的store合并成完整的store数据
     */
    private fun assembleFinalBody(
        project: Project,
        resultCommon: JsonObject?,
        resultTab: JsonObject?
    ): JsonObject {
        val userInfo = UserService.getInstance().getUserData()

        val finalBody = JsonObject()
        val commonBody =
            if (resultCommon != null && !resultCommon.isJsonNull && resultCommon.has("body")) resultCommon.getAsJsonObject("body")
            else null

        finalBody.add("common", commonBody?.get("common"))
        finalBody.add("persist", buildPersist(project, userInfo))

        if (resultTab != null) {
            val tabBody = resultTab.getAsJsonObject("body")
            finalBody.add("studio", tabBody?.get("studio"))
            finalBody.add("frontend", tabBody?.get("frontend"))
        }

        return finalBody
    }

    suspend fun getStore(project: Project? = null, selfBrowser: TocoBrowser): Any? {
        if (project == null) {
            return null
        }
        // 从框架页面中获取store
        var commonStore: JsonObject? = null
        val readyFrameBrowser = getFrames(project).find { it.getIsReady() }?.getBrowser()
        readyFrameBrowser?.let {
            commonStore = getStoreValue(readyFrameBrowser)
        }

        // 从tab页面中获取store
        var tabStore: JsonObject? = null
        val page = getRelatedPages(project, selfBrowser)?.getOrNull(0)
        if (page != null) {
            tabStore = getStoreValue(page)
        }

        // 如果结果有效，返回组装后的body
        return assembleFinalBody(project, commonStore, tabStore)
    }
}

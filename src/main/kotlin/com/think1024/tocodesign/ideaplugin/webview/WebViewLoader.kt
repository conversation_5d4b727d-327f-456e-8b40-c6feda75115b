package com.think1024.tocodesign.ideaplugin.webview

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.toco.TocoVirtualFile
import com.think1024.tocodesign.ideaplugin.utils.IdeaConfig
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.awt.*
import java.awt.event.ComponentAdapter
import java.awt.event.ComponentEvent
import javax.swing.*
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.cef.handler.CefLoadHandlerAdapter
import java.awt.event.FocusAdapter
import java.awt.event.FocusEvent
import java.awt.event.MouseEvent
import java.awt.event.MouseAdapter

// 定义加载模式枚举
enum class LoadingMode {
    INIT_PAGE_CALLBACK,  // 使用 setOnInitPageCallback 模式
    LOAD_END_HANDLER     // 使用 onLoadEnd 模式
}

class WebViewLoader(
    private val project: Project? = null,
    private val url: String,
    private val config: TocoBrowserConfig,
    private val onInitPage: (() -> Unit)? = null,
    private val loadingMode: LoadingMode = LoadingMode.INIT_PAGE_CALLBACK, // 默认使用 INIT_PAGE_CALLBACK 模式
    private val onModifiedChanged: ((modified: Boolean) -> Unit)? = null,
) {
    private val browser: TocoBrowser = TocoBrowser(project, url, config).apply {
        setOnInitPageCallback {
//            println("333, setOnInitPageCallback: ${browser.hashCode()} ${browser.cefBrowser.url}")
            if (loadingMode == LoadingMode.INIT_PAGE_CALLBACK) {
                isInitPageReceived = true
                if(!config.isOSR) {
                    ensureBrowserSize()
                }
                mainPanel.remove(loadingLabel)
                onInitPage?.invoke()
            }
        }
        setOnModifiedChanged {
            onModifiedChanged?.invoke(it)
        }
    }
    private val loadingLabel =  JLabel("${getI18nString("new.project.wizard.org.loading")}...", SwingConstants.CENTER).apply {
        val theme = IdeaConfig.getCurrentTheme()
        val isDark = theme == "DARK"
        isOpaque = true
        foreground = if (isDark) Color.WHITE else Color.BLACK
        background = if (isDark) Color(30, 30, 30, 230) else Color(255, 255, 255, 230)
        font = Font("Arial", Font.PLAIN, 16)
    }
    private val browserComponent: JComponent = browser.browser.component
    private val mainPanel = JPanel(BorderLayout()).apply {
        val theme = IdeaConfig.getCurrentTheme()
        val isDark = theme == "DARK"
        background = if (isDark) Color(38, 38, 41) else Color(255, 255, 255)
        add(browserComponent)
        if (config.isOSR) {
            add(loadingLabel, 0)
        } else {
            add(loadingLabel, -1)
        }
        addComponentListener(object : ComponentAdapter() {
            override fun componentResized(e: ComponentEvent?) {
                ensureBrowserSize()
            }
        })
        addFocusListener(object : FocusAdapter() {
            override fun focusGained(e: FocusEvent) {
                browserComponent.requestFocusInWindow()
            }
        })
        addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                browserComponent.requestFocusInWindow()
            }
        })

    }
    private var isInitPageReceived = false

    init {
        browser.browser.jbCefClient.addLoadHandler(object : CefLoadHandlerAdapter() {
            override fun onLoadEnd(browser: CefBrowser?, frame: CefFrame?, httpStatusCode: Int) {
                // 确保是主框架加载完成
                if (frame?.isMain == true) {
                    setLoading(false)
                    // 如果使用 LOAD_END_HANDLER 模式，添加加载处理器
                    if (loadingMode == LoadingMode.LOAD_END_HANDLER) {
                        SwingUtilities.invokeLater {
                            isInitPageReceived = true

                            if(!config.isOSR) {
                                ensureBrowserSize()
                            }
                            mainPanel.remove(loadingLabel)
                            onInitPage?.invoke()
                        }
                    }
                }
            }
        }, browser.browser.cefBrowser)
        
        SwingUtilities.invokeLater {
            ensureBrowserSize()
            setLoading(true)
            browser.browser.loadURL(url)
        }
    }

    private fun ensureBrowserSize() {
        // browser的侧边不能紧贴mainPanel，会导致无法往左拖大小的问题，这里给加了5的padding
        val padding = 5
        if (config.isOSR) {
            browserComponent.setBounds(padding, 0, mainPanel.size.width - padding * 2, mainPanel.size.height)
            loadingLabel.setBounds(0, 0, mainPanel.size.width, mainPanel.size.height)
            mainPanel.revalidate()
            mainPanel.repaint()
        } else {
            if (isInitPageReceived) {
                browserComponent.setBounds(padding, 0, mainPanel.size.width - padding * 2, mainPanel.size.height)
                mainPanel.revalidate();
                mainPanel.repaint();
            } else {
                browserComponent.setSize(1, 1)
            }
            loadingLabel.setBounds(0, 0, mainPanel.size.width, mainPanel.size.height)
        }
    }

    private fun setLoading(loading: Boolean) {
        if (project != null && config.file != null && config.file is TocoVirtualFile) {
            ApplicationManager.getApplication().invokeLater(Runnable {
                config.file.setLoading(loading)
                FileEditorManager.getInstance(project).updateFilePresentation(config.file)
            })
        }
    }

    fun getComponent(): JComponent = mainPanel

    fun getBrowser(): TocoBrowser = browser

    fun getIsReady(): Boolean = isInitPageReceived

    fun loadURL(newUrl: String) {
        browser.browser.loadURL(newUrl)
    }

    fun dispose() {
        browser.dispose()
    }

    fun getPreferredFocusedComponent(): JComponent? {
        return mainPanel
    }

    fun openDevTools() {
        browser.openDevTools()
    }

    /**
     * 刷新当前页面
     */
    fun refresh() {
        setLoading(true)
        browser.reload()
    }
}

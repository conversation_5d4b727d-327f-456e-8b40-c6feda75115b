package com.think1024.tocodesign.ideaplugin.listeners

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManagerListener
import com.think1024.tocodesign.ideaplugin.utils.ProjectResourceUtil
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import com.intellij.openapi.components.service
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseSearchManager
import com.think1024.tocodesign.ideaplugin.webview.WebViewManager

/**
 * ProjectCloseListener is responsible for handling project closing events in the IntelliJ IDEA plugin.
 * It implements the ProjectManagerListener interface to receive notifications about project lifecycle events.
 *
 * This listener ensures that all resources associated with a project, such as socket connections and message bus connections,
 * are properly closed when the project is being closed, preventing resource leaks and ensuring a clean shutdown.
 */
class ProjectCloseListener : ProjectManagerListener {

    private val logger = Logger.getInstance(ProjectCloseListener::class.java)

    /**
     * Called when a project is about to be closed.
     * This method is invoked by the IDE just before a project is closed.
     *
     * @param project The Project instance that is about to be closed.
     */
    override fun projectClosing(project: Project) {
        runBlocking {
            try {
                // Use a coroutine scope to launch each resource cleanup task in parallel.
                coroutineScope {

                    // Disconnect the message bus for the project to prevent message leaks.
                    launch { ProjectResourceUtil.disconnectMessageBus(project) }

                    // Cancel project ID change listeners to clean up references.
                    launch { ProjectResourceUtil.cancelProjectIdChangeListenerForProject(project) }

                    // Dispose the custom status bar widget associated with the project.
                    launch { ProjectResourceUtil.disposeTocoDesignStatusBarWidget(project) }

                    launch { WebViewManager.cleanWebViews(project) }

                    // Dispose CodeBaseSearchManager
                    launch { 
                        try {
                            val codeBaseSearchManager = project.service<CodeBaseSearchManager>()
                            codeBaseSearchManager.dispose()
                        } catch (e: Exception) {
                            logger.warn("Failed to dispose CodeBaseSearchManager", e)
                        }
                    }
                }
            } catch (e: Exception) {
                // Log a warning if any exception occurs during the resource cleanup process.
                logger.warn("Error occurred while closing project: ${project.name}", e)
            }
        }
    }
}
